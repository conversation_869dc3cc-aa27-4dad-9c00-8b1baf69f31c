import 'package:sqflite/sqflite.dart';
import 'package:flutter/foundation.dart';
import 'package:tontine_manager/core/database/database_helper.dart';
import 'package:tontine_manager/core/database/neon_service.dart';
import 'package:tontine_manager/core/storage/storage_service.dart';
import 'package:tontine_manager/shared/models/user.dart';

class AuthService {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  final NeonService _neonService = NeonService();
  bool _useFallback = false;

  /// Vérifier si SQLite est disponible, sinon utiliser le fallback
  Future<bool> _checkSQLiteAvailability() async {
    if (_useFallback) return false;

    try {
      await _dbHelper.database;
      return true;
    } catch (e) {
      print('SQLite non disponible, utilisation du fallback: $e');
      _useFallback = true;
      return false;
    }
  }

  // Créer un nouvel utilisateur
  Future<bool> createUser(User user) async {
    bool localSuccess = false;
    bool cloudSuccess = false;

    try {
      // 1. Essayer de sauvegarder localement d'abord
      final sqliteAvailable = await _checkSQLiteAvailability();

      if (sqliteAvailable) {
        final db = await _dbHelper.database;
        final result = await db.insert(
          'users',
          user.toMap(),
          conflictAlgorithm: ConflictAlgorithm.abort,
        );
        localSuccess = result > 0;
      } else {
        // Utiliser le fallback StorageService
        localSuccess = await StorageService.saveUser(user);
      }

      // 2. Essayer de synchroniser avec Neon (cloud)
      try {
        final connected = await _neonService.connect();
        if (connected) {
          cloudSuccess = await _neonService.createUser(user);
          print('Synchronisation cloud: ${cloudSuccess ? 'réussie' : 'échouée'}');
        } else {
          print('Pas de connexion cloud, sauvegarde locale uniquement');
        }
      } catch (cloudError) {
        print('Erreur synchronisation cloud: $cloudError');
        // Ne pas faire échouer la création si le cloud échoue
      }

      // Retourner true si au moins la sauvegarde locale a réussi
      return localSuccess;

    } catch (e) {
      print('Erreur lors de la création de l\'utilisateur: $e');
      // Essayer le fallback en cas d'erreur SQLite
      try {
        return await StorageService.saveUser(user);
      } catch (fallbackError) {
        print('Erreur fallback: $fallbackError');
        return false;
      }
    }
  }

  // Récupérer un utilisateur par son ID
  Future<User?> getUserById(String id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'users',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return User.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      print('Erreur lors de la récupération de l\'utilisateur: $e');
      return null;
    }
  }

  // Récupérer un utilisateur par son numéro de téléphone
  Future<User?> getUserByPhone(String phoneNumber) async {
    try {
      // 1. Essayer d'abord depuis le cloud (données les plus récentes)
      User? cloudUser;
      try {
        final connected = await _neonService.connect();
        if (connected) {
          cloudUser = await _neonService.getUserByPhone(phoneNumber);
          if (cloudUser != null) {
            print('Utilisateur trouvé dans le cloud');
            // Synchroniser avec la base locale si possible
            await _syncUserToLocal(cloudUser);
            return cloudUser;
          }
        }
      } catch (cloudError) {
        print('Erreur récupération cloud: $cloudError');
      }

      // 2. Si pas trouvé dans le cloud, chercher localement
      final sqliteAvailable = await _checkSQLiteAvailability();

      if (sqliteAvailable) {
        final db = await _dbHelper.database;
        final List<Map<String, dynamic>> maps = await db.query(
          'users',
          where: 'phone_number = ?',
          whereArgs: [phoneNumber],
          limit: 1,
        );

        if (maps.isNotEmpty) {
          final localUser = User.fromMap(maps.first);
          print('Utilisateur trouvé localement');
          return localUser;
        }
        return null;
      } else {
        // Utiliser le fallback StorageService
        return await StorageService.getUserByPhone(phoneNumber);
      }
    } catch (e) {
      print('Erreur lors de la récupération de l\'utilisateur par téléphone: $e');
      // Essayer le fallback en cas d'erreur SQLite
      try {
        return await StorageService.getUserByPhone(phoneNumber);
      } catch (fallbackError) {
        print('Erreur fallback: $fallbackError');
        return null;
      }
    }
  }

  /// Synchroniser un utilisateur du cloud vers la base locale
  Future<void> _syncUserToLocal(User user) async {
    try {
      final sqliteAvailable = await _checkSQLiteAvailability();
      if (sqliteAvailable) {
        final db = await _dbHelper.database;
        await db.insert(
          'users',
          user.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
        print('Utilisateur synchronisé localement');
      }
    } catch (e) {
      print('Erreur synchronisation locale: $e');
    }
  }

  // Récupérer un utilisateur par son ID
  Future<User?> getUserById(String id) async {
    try {
      // 1. Essayer d'abord depuis le cloud
      User? cloudUser;
      try {
        final connected = await _neonService.connect();
        if (connected) {
          cloudUser = await _neonService.getUserById(id);
          if (cloudUser != null) {
            print('Utilisateur trouvé dans le cloud par ID');
            await _syncUserToLocal(cloudUser);
            return cloudUser;
          }
        }
      } catch (cloudError) {
        print('Erreur récupération cloud par ID: $cloudError');
      }

      // 2. Chercher localement
      final sqliteAvailable = await _checkSQLiteAvailability();
      if (sqliteAvailable) {
        final db = await _dbHelper.database;
        final List<Map<String, dynamic>> maps = await db.query(
          'users',
          where: 'id = ?',
          whereArgs: [id],
          limit: 1,
        );

        if (maps.isNotEmpty) {
          return User.fromMap(maps.first);
        }
        return null;
      } else {
        return await StorageService.getUserById(id);
      }
    } catch (e) {
      print('Erreur lors de la récupération de l\'utilisateur par ID: $e');
      try {
        return await StorageService.getUserById(id);
      } catch (fallbackError) {
        print('Erreur fallback: $fallbackError');
        return null;
      }
    }
  }

  // Mettre à jour un utilisateur
  Future<bool> updateUser(User user) async {
    bool localSuccess = false;
    bool cloudSuccess = false;

    try {
      // 1. Mettre à jour localement
      final sqliteAvailable = await _checkSQLiteAvailability();
      if (sqliteAvailable) {
        final db = await _dbHelper.database;
        final result = await db.update(
          'users',
          user.toMap(),
          where: 'id = ?',
          whereArgs: [user.id],
        );
        localSuccess = result > 0;
      } else {
        // Pas de méthode updateUser dans StorageService, utiliser saveUser
        localSuccess = await StorageService.saveUser(user);
      }

      // 2. Synchroniser avec le cloud
      try {
        final connected = await _neonService.connect();
        if (connected) {
          cloudSuccess = await _neonService.updateUser(user);
          print('Mise à jour cloud: ${cloudSuccess ? 'réussie' : 'échouée'}');
        }
      } catch (cloudError) {
        print('Erreur mise à jour cloud: $cloudError');
      }

      return localSuccess;
    } catch (e) {
      print('Erreur lors de la mise à jour de l\'utilisateur: $e');
      try {
        return await StorageService.saveUser(user);
      } catch (fallbackError) {
        print('Erreur fallback mise à jour: $fallbackError');
        return false;
      }
    }
  }

  // Supprimer un utilisateur
  Future<bool> deleteUser(String id) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.delete(
        'users',
        where: 'id = ?',
        whereArgs: [id],
      );
      return result > 0;
    } catch (e) {
      print('Erreur lors de la suppression de l\'utilisateur: $e');
      return false;
    }
  }

  // Vérifier si un numéro de téléphone existe déjà
  Future<bool> phoneNumberExists(String phoneNumber) async {
    try {
      final user = await getUserByPhone(phoneNumber);
      return user != null;
    } catch (e) {
      print('Erreur lors de la vérification du numéro de téléphone: $e');
      return false;
    }
  }

  // Récupérer tous les utilisateurs (pour les tests ou l'administration)
  Future<List<User>> getAllUsers() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query('users');
      
      return List.generate(maps.length, (i) {
        return User.fromMap(maps[i]);
      });
    } catch (e) {
      print('Erreur lors de la récupération de tous les utilisateurs: $e');
      return [];
    }
  }

  // Rechercher des utilisateurs par nom
  Future<List<User>> searchUsersByName(String name) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'users',
        where: 'full_name LIKE ?',
        whereArgs: ['%$name%'],
      );
      
      return List.generate(maps.length, (i) {
        return User.fromMap(maps[i]);
      });
    } catch (e) {
      print('Erreur lors de la recherche d\'utilisateurs: $e');
      return [];
    }
  }
}
