import 'dart:async';
import 'dart:io';
import 'package:postgres/postgres.dart';
import 'package:flutter/foundation.dart';
import 'package:tontine_manager/shared/models/user.dart';

class NeonService {
  static final NeonService _instance = NeonService._internal();
  factory NeonService() => _instance;
  NeonService._internal();

  Connection? _connection;
  bool _isConnected = false;
  
  // Configuration de la base de données Neon
  static const String _host = 'ep-restless-hill-a8p5rr8i-pooler.eastus2.azure.neon.tech';
  static const String _database = 'neondb';
  static const String _username = 'neondb_owner';
  static const String _password = 'npg_rpLqThyw51Vu';
  static const int _port = 5432;

  /// Vérifier si la connexion est active
  bool get isConnected => _isConnected && _connection != null;

  /// Se connecter à la base de données Neon
  Future<bool> connect() async {
    if (_isConnected && _connection != null) {
      return true;
    }

    try {
      print('Tentative de connexion à Neon...');
      
      _connection = await Connection.open(
        Endpoint(
          host: _host,
          port: _port,
          database: _database,
          username: _username,
          password: _password,
        ),
        settings: const ConnectionSettings(
          sslMode: SslMode.require,
          connectTimeout: Duration(seconds: 10),
          queryTimeout: Duration(seconds: 30),
        ),
      );

      _isConnected = true;
      print('Connexion à Neon réussie !');
      
      // Créer les tables si elles n'existent pas
      await _createTablesIfNotExists();
      
      return true;
    } catch (e) {
      print('Erreur de connexion à Neon: $e');
      _isConnected = false;
      _connection = null;
      return false;
    }
  }

  /// Fermer la connexion
  Future<void> disconnect() async {
    if (_connection != null) {
      try {
        await _connection!.close();
      } catch (e) {
        print('Erreur lors de la fermeture de connexion: $e');
      }
    }
    _connection = null;
    _isConnected = false;
  }

  /// Créer les tables si elles n'existent pas
  Future<void> _createTablesIfNotExists() async {
    if (!isConnected) return;

    try {
      // Table des utilisateurs
      await _connection!.execute('''
        CREATE TABLE IF NOT EXISTS users (
          id TEXT PRIMARY KEY,
          phone_number TEXT UNIQUE NOT NULL,
          full_name TEXT NOT NULL,
          profile_photo TEXT,
          pin_hash TEXT NOT NULL,
          biometric_enabled BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP NOT NULL,
          last_active TIMESTAMP
        )
      ''');

      // Table des types de tontines
      await _connection!.execute('''
        CREATE TABLE IF NOT EXISTS tontine_types (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT,
          rules TEXT
        )
      ''');

      // Table des tontines
      await _connection!.execute('''
        CREATE TABLE IF NOT EXISTS tontines (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          type TEXT NOT NULL,
          creator_id TEXT NOT NULL,
          contribution_amount DECIMAL(10,2) NOT NULL,
          frequency TEXT NOT NULL,
          start_date TIMESTAMP NOT NULL,
          end_date TIMESTAMP,
          max_members INTEGER NOT NULL,
          status TEXT DEFAULT 'active',
          created_at TIMESTAMP NOT NULL,
          rules TEXT,
          FOREIGN KEY (creator_id) REFERENCES users (id)
        )
      ''');

      // Table des membres
      await _connection!.execute('''
        CREATE TABLE IF NOT EXISTS members (
          id TEXT PRIMARY KEY,
          tontine_id TEXT NOT NULL,
          user_id TEXT NOT NULL,
          role TEXT NOT NULL,
          join_date TIMESTAMP NOT NULL,
          position_in_cycle INTEGER,
          is_active BOOLEAN DEFAULT TRUE,
          FOREIGN KEY (tontine_id) REFERENCES tontines (id),
          FOREIGN KEY (user_id) REFERENCES users (id)
        )
      ''');

      // Table des contributions
      await _connection!.execute('''
        CREATE TABLE IF NOT EXISTS contributions (
          id TEXT PRIMARY KEY,
          tontine_id TEXT NOT NULL,
          member_id TEXT NOT NULL,
          amount DECIMAL(10,2) NOT NULL,
          contribution_date TIMESTAMP NOT NULL,
          status TEXT DEFAULT 'pending',
          payment_method TEXT,
          notes TEXT,
          FOREIGN KEY (tontine_id) REFERENCES tontines (id),
          FOREIGN KEY (member_id) REFERENCES members (id)
        )
      ''');

      print('Tables créées avec succès dans Neon');
    } catch (e) {
      print('Erreur lors de la création des tables: $e');
      rethrow;
    }
  }

  /// Créer un utilisateur dans Neon
  Future<bool> createUser(User user) async {
    if (!isConnected) {
      print('Pas de connexion à Neon');
      return false;
    }

    try {
      await _connection!.execute(
        Sql.named('''
          INSERT INTO users (id, phone_number, full_name, profile_photo, pin_hash, biometric_enabled, created_at, last_active)
          VALUES (@id, @phone_number, @full_name, @profile_photo, @pin_hash, @biometric_enabled, @created_at, @last_active)
        '''),
        parameters: {
          'id': user.id,
          'phone_number': user.phoneNumber,
          'full_name': user.fullName,
          'profile_photo': user.profilePhoto,
          'pin_hash': user.pinHash,
          'biometric_enabled': user.biometricEnabled,
          'created_at': user.createdAt.toIso8601String(),
          'last_active': user.lastActive?.toIso8601String(),
        },
      );
      
      print('Utilisateur créé dans Neon: ${user.phoneNumber}');
      return true;
    } catch (e) {
      print('Erreur lors de la création utilisateur dans Neon: $e');
      return false;
    }
  }

  /// Récupérer un utilisateur par numéro de téléphone
  Future<User?> getUserByPhone(String phoneNumber) async {
    if (!isConnected) {
      print('Pas de connexion à Neon');
      return null;
    }

    try {
      final result = await _connection!.execute(
        Sql.named('SELECT * FROM users WHERE phone_number = @phone_number LIMIT 1'),
        parameters: {'phone_number': phoneNumber},
      );

      if (result.isNotEmpty) {
        final row = result.first;
        return User(
          id: row[0] as String,
          phoneNumber: row[1] as String,
          fullName: row[2] as String,
          profilePhoto: row[3] as String?,
          pinHash: row[4] as String,
          biometricEnabled: row[5] as bool,
          createdAt: DateTime.parse(row[6] as String),
          lastActive: row[7] != null ? DateTime.parse(row[7] as String) : null,
        );
      }
      
      return null;
    } catch (e) {
      print('Erreur lors de la récupération utilisateur: $e');
      return null;
    }
  }

  /// Récupérer un utilisateur par ID
  Future<User?> getUserById(String id) async {
    if (!isConnected) {
      print('Pas de connexion à Neon');
      return null;
    }

    try {
      final result = await _connection!.execute(
        Sql.named('SELECT * FROM users WHERE id = @id LIMIT 1'),
        parameters: {'id': id},
      );

      if (result.isNotEmpty) {
        final row = result.first;
        return User(
          id: row[0] as String,
          phoneNumber: row[1] as String,
          fullName: row[2] as String,
          profilePhoto: row[3] as String?,
          pinHash: row[4] as String,
          biometricEnabled: row[5] as bool,
          createdAt: DateTime.parse(row[6] as String),
          lastActive: row[7] != null ? DateTime.parse(row[7] as String) : null,
        );
      }
      
      return null;
    } catch (e) {
      print('Erreur lors de la récupération utilisateur par ID: $e');
      return null;
    }
  }

  /// Mettre à jour un utilisateur
  Future<bool> updateUser(User user) async {
    if (!isConnected) {
      print('Pas de connexion à Neon');
      return false;
    }

    try {
      await _connection!.execute(
        Sql.named('''
          UPDATE users 
          SET full_name = @full_name, 
              profile_photo = @profile_photo, 
              pin_hash = @pin_hash, 
              biometric_enabled = @biometric_enabled, 
              last_active = @last_active
          WHERE id = @id
        '''),
        parameters: {
          'id': user.id,
          'full_name': user.fullName,
          'profile_photo': user.profilePhoto,
          'pin_hash': user.pinHash,
          'biometric_enabled': user.biometricEnabled,
          'last_active': user.lastActive?.toIso8601String(),
        },
      );
      
      print('Utilisateur mis à jour dans Neon: ${user.phoneNumber}');
      return true;
    } catch (e) {
      print('Erreur lors de la mise à jour utilisateur: $e');
      return false;
    }
  }

  /// Tester la connexion
  Future<bool> testConnection() async {
    try {
      if (!isConnected) {
        final connected = await connect();
        if (!connected) return false;
      }

      final result = await _connection!.execute('SELECT version()');
      print('Test de connexion réussi. Version PostgreSQL: ${result.first[0]}');
      return true;
    } catch (e) {
      print('Test de connexion échoué: $e');
      return false;
    }
  }
}
