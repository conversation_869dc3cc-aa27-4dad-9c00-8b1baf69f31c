import 'dart:io';
import 'lib/core/database/neon_service.dart';
import 'lib/shared/models/user.dart';
import 'package:uuid/uuid.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

void main() async {
  print('🚀 Test de connexion à Neon Database');
  print('=====================================');
  
  final neonService = NeonService();
  
  try {
    // Test 1: Connexion de base
    print('\n1️⃣ Test de connexion...');
    final connected = await neonService.testConnection();
    if (connected) {
      print('✅ Connexion réussie !');
    } else {
      print('❌ Échec de la connexion');
      return;
    }
    
    // Test 2: Création d'un utilisateur
    print('\n2️⃣ Test de création d\'utilisateur...');
    final testUser = User(
      id: const Uuid().v4(),
      phoneNumber: '221${DateTime.now().millisecondsSinceEpoch.toString().substring(7)}',
      fullName: 'Test User Neon',
      pinHash: _hashPin('1234'),
      createdAt: DateTime.now(),
    );
    
    final createSuccess = await neonService.createUser(testUser);
    if (createSuccess) {
      print('✅ Utilisateur créé: ${testUser.phoneNumber}');
    } else {
      print('❌ Échec de la création');
      return;
    }
    
    // Test 3: Récupération par téléphone
    print('\n3️⃣ Test de récupération par téléphone...');
    final retrievedUser = await neonService.getUserByPhone(testUser.phoneNumber);
    if (retrievedUser != null) {
      print('✅ Utilisateur récupéré: ${retrievedUser.fullName}');
      print('   📱 Téléphone: ${retrievedUser.phoneNumber}');
      print('   🆔 ID: ${retrievedUser.id}');
    } else {
      print('❌ Utilisateur non trouvé');
      return;
    }
    
    // Test 4: Récupération par ID
    print('\n4️⃣ Test de récupération par ID...');
    final userById = await neonService.getUserById(testUser.id);
    if (userById != null) {
      print('✅ Utilisateur récupéré par ID: ${userById.fullName}');
    } else {
      print('❌ Utilisateur non trouvé par ID');
      return;
    }
    
    // Test 5: Mise à jour
    print('\n5️⃣ Test de mise à jour...');
    final updatedUser = testUser.copyWith(
      fullName: 'Test User Neon Updated',
      lastActive: DateTime.now(),
    );
    
    final updateSuccess = await neonService.updateUser(updatedUser);
    if (updateSuccess) {
      print('✅ Utilisateur mis à jour');
      
      // Vérifier la mise à jour
      final verifyUser = await neonService.getUserById(testUser.id);
      if (verifyUser != null && verifyUser.fullName == updatedUser.fullName) {
        print('✅ Mise à jour vérifiée: ${verifyUser.fullName}');
      } else {
        print('❌ Mise à jour non vérifiée');
      }
    } else {
      print('❌ Échec de la mise à jour');
    }
    
    print('\n🎉 Tous les tests sont passés avec succès !');
    print('✅ La base de données Neon est opérationnelle');
    
  } catch (e) {
    print('\n❌ Erreur lors des tests: $e');
    print('Stack trace: ${StackTrace.current}');
  } finally {
    // Fermer la connexion
    await neonService.disconnect();
    print('\n🔌 Connexion fermée');
  }
}

String _hashPin(String pin) {
  final bytes = utf8.encode(pin);
  final digest = sha256.convert(bytes);
  return digest.toString();
}
