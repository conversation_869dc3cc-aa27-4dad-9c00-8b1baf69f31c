import 'package:flutter/material.dart';
import 'package:tontine_manager/core/database/neon_service.dart';
import 'package:tontine_manager/shared/models/user.dart';
import 'package:uuid/uuid.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

class NeonTestScreen extends StatefulWidget {
  const NeonTestScreen({super.key});

  @override
  State<NeonTestScreen> createState() => _NeonTestScreenState();
}

class _NeonTestScreenState extends State<NeonTestScreen> {
  final NeonService _neonService = NeonService();
  final TextEditingController _logController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _logController.dispose();
    super.dispose();
  }

  void _addLog(String message) {
    setState(() {
      _logController.text += '${DateTime.now().toIso8601String()}: $message\n';
    });
    // <PERSON>e défiler vers le bas
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _logController.selection = TextSelection.fromPosition(
        TextPosition(offset: _logController.text.length),
      );
    });
  }

  void _clearLogs() {
    setState(() {
      _logController.clear();
    });
  }

  Future<void> _testConnection() async {
    setState(() => _isLoading = true);
    _addLog('🔄 Test de connexion à Neon...');

    try {
      final success = await _neonService.testConnection();
      if (success) {
        _addLog('✅ Connexion à Neon réussie !');
      } else {
        _addLog('❌ Échec de la connexion à Neon');
      }
    } catch (e) {
      _addLog('❌ Erreur de connexion: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testCreateUser() async {
    setState(() => _isLoading = true);
    _addLog('🔄 Test de création d\'utilisateur...');

    try {
      // Créer un utilisateur de test
      final testUser = User(
        id: const Uuid().v4(),
        phoneNumber: '************',
        fullName: 'Test Neon User',
        pinHash: _hashPin('1234'),
        createdAt: DateTime.now(),
      );

      final success = await _neonService.createUser(testUser);
      if (success) {
        _addLog('✅ Utilisateur créé avec succès dans Neon');
        _addLog('📱 Téléphone: ${testUser.phoneNumber}');
        _addLog('👤 Nom: ${testUser.fullName}');
        _addLog('🆔 ID: ${testUser.id}');
      } else {
        _addLog('❌ Échec de la création d\'utilisateur');
      }
    } catch (e) {
      _addLog('❌ Erreur création utilisateur: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testGetUser() async {
    setState(() => _isLoading = true);
    _addLog('🔄 Test de récupération d\'utilisateur...');

    try {
      final user = await _neonService.getUserByPhone('************');
      if (user != null) {
        _addLog('✅ Utilisateur trouvé dans Neon');
        _addLog('👤 Nom: ${user.fullName}');
        _addLog('📱 Téléphone: ${user.phoneNumber}');
        _addLog('📅 Créé le: ${user.createdAt}');
        _addLog('🔐 Biométrie: ${user.biometricEnabled ? 'Activée' : 'Désactivée'}');
      } else {
        _addLog('❌ Utilisateur non trouvé');
      }
    } catch (e) {
      _addLog('❌ Erreur récupération utilisateur: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testFullWorkflow() async {
    setState(() => _isLoading = true);
    _addLog('🔄 Test du workflow complet...');

    try {
      // 1. Test de connexion
      _addLog('1️⃣ Test de connexion...');
      final connected = await _neonService.testConnection();
      if (!connected) {
        _addLog('❌ Connexion échouée, arrêt du test');
        return;
      }
      _addLog('✅ Connexion OK');

      // 2. Création d'un utilisateur unique
      _addLog('2️⃣ Création d\'un utilisateur...');
      final uniqueId = const Uuid().v4();
      final testUser = User(
        id: uniqueId,
        phoneNumber: '221${DateTime.now().millisecondsSinceEpoch.toString().substring(7)}',
        fullName: 'Test Workflow User',
        pinHash: _hashPin('1234'),
        createdAt: DateTime.now(),
      );

      final createSuccess = await _neonService.createUser(testUser);
      if (!createSuccess) {
        _addLog('❌ Création échouée');
        return;
      }
      _addLog('✅ Utilisateur créé: ${testUser.phoneNumber}');

      // 3. Récupération par téléphone
      _addLog('3️⃣ Récupération par téléphone...');
      final retrievedUser = await _neonService.getUserByPhone(testUser.phoneNumber);
      if (retrievedUser == null) {
        _addLog('❌ Récupération échouée');
        return;
      }
      _addLog('✅ Utilisateur récupéré: ${retrievedUser.fullName}');

      // 4. Récupération par ID
      _addLog('4️⃣ Récupération par ID...');
      final userById = await _neonService.getUserById(testUser.id);
      if (userById == null) {
        _addLog('❌ Récupération par ID échouée');
        return;
      }
      _addLog('✅ Utilisateur récupéré par ID: ${userById.fullName}');

      // 5. Mise à jour
      _addLog('5️⃣ Mise à jour de l\'utilisateur...');
      final updatedUser = testUser.copyWith(
        fullName: 'Test Workflow User Updated',
        lastActive: DateTime.now(),
      );
      final updateSuccess = await _neonService.updateUser(updatedUser);
      if (!updateSuccess) {
        _addLog('❌ Mise à jour échouée');
        return;
      }
      _addLog('✅ Utilisateur mis à jour');

      _addLog('🎉 Workflow complet réussi !');
    } catch (e) {
      _addLog('❌ Erreur dans le workflow: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  String _hashPin(String pin) {
    final bytes = utf8.encode(pin);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Neon Database'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Boutons de test
            Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: [
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _testConnection,
                  icon: const Icon(Icons.wifi),
                  label: const Text('Test Connexion'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _testCreateUser,
                  icon: const Icon(Icons.person_add),
                  label: const Text('Créer Utilisateur'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _testGetUser,
                  icon: const Icon(Icons.search),
                  label: const Text('Récupérer Utilisateur'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _testFullWorkflow,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Test Complet'),
                ),
                ElevatedButton.icon(
                  onPressed: _clearLogs,
                  icon: const Icon(Icons.clear),
                  label: const Text('Effacer Logs'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Zone de logs
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(8),
                      decoration: const BoxDecoration(
                        color: Colors.grey,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(8),
                          topRight: Radius.circular(8),
                        ),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.terminal, size: 16),
                          const SizedBox(width: 8),
                          const Text(
                            'Logs de Test',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const Spacer(),
                          if (_isLoading)
                            const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: TextField(
                        controller: _logController,
                        maxLines: null,
                        readOnly: true,
                        style: const TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 12,
                        ),
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.all(8),
                          hintText: 'Les logs de test apparaîtront ici...',
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
